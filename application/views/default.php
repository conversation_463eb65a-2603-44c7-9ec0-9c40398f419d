<!DOCTYPE html>
<html lang="<?php echo Arr::get(Kohana::config('app.html_lang'), $info['lang'], $info['lang']); ?>" data-default_lang="<?php echo Arr::get(Kohana::config('app.html_lang'), Kohana::config('app.language'), Kohana::config('app.language')); ?>" data-currency_code="<?php echo $currency['code']; ?>" data-currency_display="<?php echo $currency['display']; ?>" data-currency_exchange="<?php echo $currency['exchange']; ?>" data-webshop_min_order="<?php echo intval(Arr::get($shopping_cart, 'total_extra_shipping_min_total', '0')); ?>"
	  data-kne_use_double_pricetags="<?php echo (Kohana::config('app.utils.kn_euro_conversion.use_double_pricetags')) ?? 1; ?>"
      data-kne_conversion_executed="<?php echo ((isset($kn_euro_conversion_executed)) ? (int)$kn_euro_conversion_executed : (Kohana::config('app.utils.kn_euro_conversion.conversion_executed'))); ?>"
      data-kne_conversion_exchange="<?php echo (Kohana::config('app.utils.kn_euro_conversion.conversion_exchange')) ?? 7.5345; ?>"
      data-kne_conversion_date="<?php echo (strtotime((!empty($kn_euro_conversion_date)) ? $kn_euro_conversion_date : '2023-01-01 00:00:00')) * 1000  ; ?>"
      data-kne_double_prices_begin_date="<?php echo (strtotime((Kohana::config('app.utils.kn_euro_conversion.double_prices_begin_date')) ?? '2022-09-05 00:00:00')) * 1000 ; ?>"
      data-kne_double_prices_end_date="<?php echo (strtotime((Kohana::config('app.utils.kn_euro_conversion.double_prices_end_date')) ?? '2023-12-31 23:59:59')) * 1000 ; ?>"
      data-kne_kn_format="<?php echo Catalog::currency('HRK', 'display') ?? '%s kn'; ?>"
      data-kne_euro_format="<?php echo Catalog::currency('EUR', 'display') ?? '%s €'; ?>"
      data-kne_display_format="<?php echo (Kohana::config('app.utils.kn_euro_conversion.display_format.standard')) ?? ''; ?>"
      data-kne_second_price_to_front="<?php echo Kohana::config('app.utils.kn_euro_conversion.converted_price_show_in_front'); ?>"
>
<head>
    <?php if (Kohana::$environment === 1): ?>
        <?php echo Google::tag_manager($info['gtagmanager_code'], 'head'); ?>
    <?php endif; ?>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
	<link rel="dns-prefetch" href="//www.google-analytics.com">
	<link rel="dns-prefetch" href="//ssl.google-analytics.com">
	<link rel="dns-prefetch" href="//connect.facebook.net">
	<link rel="dns-prefetch" href="//static.ak.facebook.com">
	<link rel="dns-prefetch" href="//s-static.ak.facebook.com">
	<link rel="dns-prefetch" href="//fbstatic-a.akamaihd.net">
	<link rel="dns-prefetch" href="//maps.gstatic.com">
	<link rel="dns-prefetch" href="//maps.google.com">
	<link rel="dns-prefetch" href="//maps.googleapis.com">
	<link rel="dns-prefetch" href="//mt0.googleapis.com">
	<link rel="dns-prefetch" href="//mt1.googleapis.com">
	<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
	<link rel="manifest" href="/manifest.json">
	<link rel="mask-icon" href="/safari-pinned-tab.svg" color="#5bbad5">
	<meta name="theme-color" content="#ffffff">
	<meta name="msapplication-navbutton-color" content="#232323">
	<meta name="apple-mobile-web-app-status-bar-style" content="#232323">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
	<title><?php $this->block('title'); ?><?php $this->endblock('title'); ?></title>
	<?php if (Kohana::$environment !== 1): ?><meta name="robots" content="noindex, nofollow"><?php endif; ?>
	<?php $this->block('seo'); ?><?php $this->endblock('seo'); ?>
	<?php echo Html::media('js_gmodernizr'); ?>
	<?php echo Html::media('css_gdefault'); ?>
	<?php if (Kohana::$environment === 1): ?><?php echo Google::universal_analytics($info['ganalytics_code'], FALSE, $info['controller'].'_'.$info['action']); ?><?php endif; ?>
	<?php $this->block('extrahead'); ?><?php $this->endblock('extrahead'); ?>
</head>
<body class="<?php echo $info['page_class']; ?><?php $this->block('page_class'); ?><?php $this->endblock('page_class'); ?>" data-module="<?php echo $info['controller']; ?>">
    <?php if (Kohana::$environment === 1 ): ?>
        <?php echo Google::tag_manager($info['gtagmanager_code'], 'body'); ?>
		<?php if (!empty($gdpr_approved) AND is_array($gdpr_approved) AND in_array('marketing', $gdpr_approved)): ?>	
			<?php echo Facebook::init('2006664706258787', $info['lang']); ?>
		<?php endif; ?>
    <?php endif; ?>
	<div class="overflow">
	<?php $this->block('header'); ?>
		
		<?php $user_level = ''; ?>
		<?php if(!empty($user)): ?>	
			<?php $user_level = $user->level; ?>
		<?php endif; ?>

		<?php $active_menu_item = Utils::active_urls($info['lang'], $info['cmspage_url']); ?>
		<?php $menu_one = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => 'cms_osnovni', 'selected' => $active_menu_item, 'level_range' => 1.1)); ?>
		<?php $menu_two = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => 'cms_djelatnik', 'selected' => $active_menu_item, 'level_range' => 1.1)); ?>
		<?php $menu_tree = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => 'cms_zaposlenik', 'selected' => $active_menu_item, 'level_range' => 1.1)); ?>
					
		<div class="mobile-top-menu">
			<ul class="mobile-top-menu-list">
				<?php if(!empty($menu_one)): ?>
					<?php echo View::factory('cms/widget/menu', ['items' => $menu_one]); ?>
					<?php if($user_level >= 3): ?>
						<?php echo View::factory('cms/widget/menu', ['items' => $menu_two]); ?>
					<?php endif; ?>
					<?php if($user_level > 3): ?>
						<?php echo View::factory('cms/widget/menu', ['items' => $menu_tree]); ?>
					<?php endif; ?>
					<li><?php echo Arr::get($cmslabel, 'contact_menu'); ?></li>
				<?php endif; ?>
			</ul>
			<div class="mobile-top-menu-bottom">
				<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'login', FALSE); ?>" class="btn-border"><?php echo Arr::get($cmslabel, 'login_employees'); ?></a>
				<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'login', FALSE); ?>" class="btn-border"><?php echo Arr::get($cmslabel, 'login_employees_2'); ?></a>
				<div class="item-tel"><?php echo Arr::get($cmslabel, 'contact_tel'); ?></div>
				<div class="item-mail"><?php echo Arr::get($cmslabel, 'contact_mail'); ?></div>
				<div class="social"><?php echo Arr::get($cmslabel, 'social'); ?></div>
			</div>
		</div>

		<header class="header">
			<div class="wrapper">
				<a href="<?php echo Utils::homepage($info['lang']); ?>" class="logo"></a>
				
				<?php $top_menu = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => 'top', 'selected' => $active_menu_item, 'level_range' => 1.1)); ?>
				<?php $top_menu_two = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => 'top_djelatnici', 'selected' => $active_menu_item, 'level_range' => 1.1)); ?>
				
				<ul class="nav-top">
					<?php foreach ($top_menu as $menu_item): ?>	
						<?php $submenu = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => 'top', 'selected' => $active_menu_item, 'level_range' => 2.2, 'start_position' => $menu_item['position_h'])); ?>
						<li class="<?php if($info['basic_url'] == $menu_item['url']): ?>selected<?php endif; ?><?php if(!empty($submenu)): ?> has-children<?php endif; ?>">
							<a href="<?php echo $menu_item['url']; ?>"><span><?php echo $menu_item['title']; ?></span></a>
							<?php if(!empty($submenu)): ?>
								<ul>
									<?php foreach ($submenu as $submenu_item): ?>
										<li<?php if($info['url'] == $submenu_item['url']): ?> class="selected"<?php endif; ?>><a href="<?php echo $submenu_item['url']; ?>"><span><?php echo $submenu_item['title']; ?></span></a></li>
									<?php endforeach; ?>
								</ul>
							<?php endif; ?>
						</li>
					<?php endforeach; ?>
					<?php if($user_level >= 3): ?>
						<?php foreach ($top_menu_two as $menu_item_two): ?>
							<?php $submenu = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => 'top_djelatnici', 'selected' => $active_menu_item, 'level_range' => 2.2, 'start_position' => $menu_item['position_h'])); ?>
							<li class="<?php if($info['basic_url'] == $menu_item_two['url']): ?>selected<?php endif; ?><?php if(!empty($submenu)): ?> has-children<?php endif; ?>">
								<a href="<?php echo $menu_item_two['url']; ?>"><span><?php echo $menu_item_two['title']; ?></span></a>
								<?php if(!empty($submenu)): ?>
									<ul>
										<?php foreach ($submenu as $submenu_item): ?>
											<li<?php if($info['url'] == $submenu_item['url']): ?> class="selected"<?php endif; ?>><a href="<?php echo $submenu_item['url']; ?>"><span><?php echo $submenu_item['title']; ?></span></a></li>
										<?php endforeach; ?>
									</ul>
								<?php endif; ?>
							</li>
						<?php endforeach; ?>
					<?php endif; ?>
					<li<?php if($info['basic_url'] == '/kontakt/'): ?> class="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'contact_menu'); ?></li>
				</ul>

				<ul class="header-contact">
					<li class="item-tel"><?php echo Arr::get($cmslabel, 'contact_tel'); ?></li>
					<li class="item-mail"><?php echo Arr::get($cmslabel, 'contact_mail'); ?></li>		
				</ul>

				<?php echo View::factory('auth/widget/user_box'); ?>

				<?php echo View::factory('webshop/widget/shopping_cart'); ?>

				<a href="javascript:toggleBox(['.m-menu', '.nav-top', '.mobile-top-menu', '.header', 'body']);" class="m-menu"></a>

			</div>	
		</header>

		<div class="categories-cnt">
			<div class="wrapper">
				<?php $categories = Widget_Catalog::categories(array('lang' => $info['lang'], 'level_range' => '1.1')); ?>
				<?php if(!empty($categories)): ?>
					<ul class="categories">
						<?php foreach ($categories as $category): ?>
							<?php $subcategories = Widget_Catalog::categories(array('lang' => $info['lang'], 'level_range' => '2.2', 'start_position' => $category['position_h'])); ?>
							<li class="<?php if(!empty($subcategories)): ?>has-children<?php endif; ?>">
								<a href="<?php echo $category['url']; ?>"><span><?php echo $category['title']; ?></span></a>
								<?php if(!empty($subcategories)): ?>
									<ul>
										<?php foreach ($subcategories as $subcategory): ?>
											<?php $subsubcategories = Widget_Catalog::categories(array('lang' => $info['lang'], 'level_range' => '3.3', 'start_position' => $subcategory['position_h'])); ?>
											<li class="<?php if(!empty($subsubcategories)): ?>has-children<?php endif; ?>">
												<a href="<?php echo $subcategory['url']; ?>"><?php echo $subcategory['title']; ?></a>
												<?php if(!empty($subsubcategories)): ?>
													<ul>
														<?php foreach ($subsubcategories as $subsubcategory): ?>
															<?php $categories_l4 = Widget_Catalog::categories(array('lang' => $info['lang'], 'level_range' => '4.4', 'start_position' => $subsubcategory['position_h'])); ?>
															<li class="<?php if(!empty($categories_l4)): ?>has-children<?php endif; ?>">
																<a href="<?php echo $subsubcategory['url']; ?>"><?php echo $subsubcategory['title']; ?></a>
																<?php if(!empty($categories_l4)): ?>
																	<ul>
																		<?php foreach ($categories_l4 as $category_l4): ?>
																			<li>
																				<a href="<?php echo $category_l4['url']; ?>"><?php echo $category_l4['title']; ?></a>
																			</li>
																		<?php endforeach; ?>
																		<li><a href="<?php echo $subsubcategory['url']; ?>"><?php echo Arr::get($cmslabel, 'all_products_in_category'); ?></a></li>
																	</ul>
																<?php endif; ?>
															</li>
														<?php endforeach; ?>
														<li><a href="<?php echo $subcategory['url']; ?>"><?php echo Arr::get($cmslabel, 'all_products_in_category'); ?></a></li>
													</ul>
												<?php endif; ?>
											</li>
										<?php endforeach; ?>
										<li><a href="<?php echo $category['url']; ?>"><span><?php echo Arr::get($cmslabel, 'all_products_in_category'); ?></span></a></li>
									</ul>
								<?php endif; ?>
							</li>
						<?php endforeach; ?>
					</ul>
				<?php endif ?>

				<?php echo View::factory('search/widget/form'); ?>
			</div>
		</div>
	<?php $this->endblock('header'); ?>

	<div class="main wrapper clear <?php $this->block('main_class'); ?><?php $this->endblock('main_class'); ?>">
		<?php $this->block('content_layout'); ?>
			<?php $this->block('search_top_container'); ?><?php $this->endblock('search_top_container'); ?>
			<div class="main-content<?php $this->block('main_content_class'); ?><?php $this->endblock('main_content_class'); ?>">
				<div class="clear wrapper2">
					<div class="bc">
						<?php $this->block('breadcrumb'); ?>
							<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url']); ?>
							<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
						<?php $this->endblock('breadcrumb'); ?>
					</div>

					<?php $this->block('content'); ?><?php $this->endblock('content'); ?>
				</div>		
			</div>

			<?php $this->block('sidebar'); ?>
				<aside class="sidebar">
					<?php $active_menu_item = Utils::active_urls($info['lang'], $info['cmspage_url']); ?>

					<?php if(!empty($menu_one)): ?>
						<ul class="cms-nav">
							<?php echo View::factory('cms/widget/menu', ['items' => $menu_one]); ?>
							<?php if($user_level >= 3): ?>
								<?php echo View::factory('cms/widget/menu', ['items' => $menu_two]); ?>
							<?php endif; ?>
							<?php if($user_level > 3): ?>
								<?php echo View::factory('cms/widget/menu', ['items' => $menu_tree]); ?>
							<?php endif; ?>
							<li><?php echo Arr::get($cmslabel, 'contact_menu'); ?></li>
						</ul>
					<?php endif; ?>
				</aside>
			<?php $this->endblock('sidebar'); ?>
		<?php $this->endblock('content_layout'); ?>
	</div>
	
	<?php $this->block('newsletter'); ?>
		<?php echo View::factory('newsletter/widget/manage'); ?>
	<?php $this->endblock('newsletter'); ?>
	
	<?php $this->block('footer'); ?>
		<footer class="footer">
			<div class="clear wrapper">
				<div class="footer-col footer-col1">
					<div class="footer-title"><?php echo Arr::get($cmslabel, 'footer_title_stoma'); ?></div>
					<?php $footer_menu = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => 'footer', 'selected' => $active_menu_item)); ?>
					<?php $footer_menu_two = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => 'footer_djelatnici', 'selected' => $active_menu_item)); ?>
					
					<?php if($footer_menu): ?>
						<ul class="nav-footer">
							<?php foreach ($footer_menu as $menu_item): ?>
								<li class="<?php if($info['basic_url'] == $menu_item['url']): ?>selected<?php endif; ?>"><a href="<?php echo $menu_item['url']; ?>" title="<?php echo $menu_item['anchor_text']; ?>"<?php if($menu_item['target_blank']): ?> target="_blank"<?php endif; ?><?php if($info['basic_url'] == $menu_item['url']): ?> class="active"<?php endif; ?>><?php echo $menu_item['title']; ?></a></li>
							<?php endforeach; ?>
							<?php if($user_level >= 3): ?>
								<?php foreach ($footer_menu_two as $footer_item_two): ?>
									<li class="<?php if($info['basic_url'] == $menu_item['url']): ?>selected<?php endif; ?>"><a href="<?php echo $footer_item_two['url']; ?>" <?php if($info['basic_url'] == $footer_item_two['url']): ?> class="active"<?php endif; ?>><?php echo $footer_item_two['title']; ?></a></li>
								<?php endforeach; ?>
							<?php endif; ?>
							<li<?php if($info['url'] == '/kontakt/'): ?> class="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'contact_menu'); ?></li>
							<li class="gdpr-link"><a class="footer-gdpr-popup-btn-edit gdpr_configurator_button" href="javascript:void(0);" id="gdpr_configurator_button"><?php echo Arr::get($cmslabel, 'gdpr_link'); ?></a></li>
						</ul>
					<?php endif; ?>

				</div>
				<div class="footer-col footer-col2">
					<div class="footer-title"><?php echo Arr::get($cmslabel, 'footer_title_terms'); ?></div>
					<?php $footer_menu = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => 'footer_terms', 'selected' => $active_menu_item)); ?>
					<?php if($footer_menu): ?>
						<ul class="nav-footer">
							<?php foreach ($footer_menu as $menu_item): ?>
								<li><a href="<?php echo $menu_item['url']; ?>" title="<?php echo $menu_item['anchor_text']; ?>"<?php if($menu_item['target_blank']): ?> target="_blank"<?php endif; ?><?php if($info['basic_url'] == $menu_item['url']): ?> class="active"<?php endif; ?>><?php echo $menu_item['title']; ?></a></li>
							<?php endforeach; ?>
						</ul>
					<?php endif; ?>	
				</div>
				<div class="footer-col footer-col3">
					<div class="footer-title"><?php echo Arr::get($cmslabel, 'footer_title_contact'); ?></div>
					<ul class="footer-contact">
						<li class="item-tel"><?php echo Arr::get($cmslabel, 'contact_tel'); ?></li>
						<li class="item-mail"><?php echo Arr::get($cmslabel, 'contact_mail'); ?></li>		
					</ul>
					<div class="social">
						<?php echo Arr::get($cmslabel, 'social'); ?>
					</div>		
				</div>
				<div class="footer-col footer-col4">
					<?php echo Arr::get($cmslabel, 'footer_logos'); ?>
				</div>
				<div class="footer-col footer-col5">
					<?php echo Arr::get($cmslabel, 'footer_logos_payment'); ?>		
				</div>

				<div class="footer-bottom">
					<div class="footer-login">
						<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'login', FALSE); ?>" class="btn-border btn-footer"><?php echo Arr::get($cmslabel, 'login_employees'); ?></a>
						<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'login', FALSE); ?>" class="btn-border btn-footer"><?php echo Arr::get($cmslabel, 'login_employees_2'); ?></a>
					</div>			
					<div class="copy"><?php echo str_replace('%YEAR%', date('Y'), Arr::get($cmslabel, 'copyright', '')); ?></div>
					<div class="dev"><?php echo Kohana::config('signature.webshop.'.$info['lang']); ?></div>
				</div>
			</div>
		</footer>
	<?php $this->endblock('footer'); ?>
	</div>
	<?php echo View::factory('gdpr/widget/configurator'); ?>
	<?php echo Html::media('js_gdefault'); ?>
	<?php echo View::factory('admin/widget_fe/toolbar'); ?>
	<?php $this->block('extrabody'); ?><?php $this->endblock('extrabody'); ?>
</body>
</html>